"""
Configuration settings for the eChemPortal scraper
"""
import os
from pathlib import Path

# Base URLs
ECHEMPORTAL_BASE_URL = "https://echemportal.org/echemportal/property-search"
ECHA_BASE_URL = "https://echa.europa.eu"

# Request settings
REQUEST_TIMEOUT = 30
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds

# Rate limiting (be respectful to the servers)
DELAY_BETWEEN_REQUESTS = 2  # seconds
DELAY_BETWEEN_PAGES = 3     # seconds
DELAY_BETWEEN_ECHA_REQUESTS = 1  # seconds

# User agents for rotation
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
]

# Selenium settings
SELENIUM_TIMEOUT = 20
HEADLESS_MODE = True
SELENIUM_PAGE_LOAD_TIMEOUT = 30

# Data storage settings
DATA_DIR = Path("data")
OUTPUT_DIR = Path("output")
LOGS_DIR = Path("logs")

# Create directories if they don't exist
for directory in [DATA_DIR, OUTPUT_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)

# Output file settings
OUTPUT_CSV_FILE = OUTPUT_DIR / "echemportal_acute_toxicity_data.csv"
PROGRESS_FILE = DATA_DIR / "scraping_progress.json"
ERROR_LOG_FILE = LOGS_DIR / "errors.log"

# Scraping parameters
MAX_PAGES_TO_SCRAPE = None  # None means scrape all pages, set to a number for testing
SAVE_PROGRESS_EVERY = 10    # Save progress every N pages
BATCH_SIZE = 50             # Process substances in batches

# Target property and source filters
TARGET_PROPERTY = "Acute toxicity: oral"
TARGET_SOURCE = "ECHA REACH"

# ECHA page parsing settings
EFFECT_LEVELS_KEYWORDS = ["Effect levels", "Effect level", "Results and discussion"]

# Logging settings
LOG_LEVEL = "INFO"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
