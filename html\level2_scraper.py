"""
Level 2 Scraper: ECHA registration dossier pages
Extracts detailed Effect levels data from ECHA pages
"""
import time
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin
from loguru import logger
from bs4 import BeautifulSoup, Tag
import requests

import config
from utils import ScrapingUtils, clean_text


class Level2Scraper:
    """Scraper for ECHA registration dossier detail pages"""
    
    def __init__(self):
        self.utils = ScrapingUtils()
    
    def scrape_echa_page(self, url: str, substance_name: str = "", cas_number: str = "") -> List[Dict[str, Any]]:
        """
        Scrape ECHA page for Effect levels data
        
        Args:
            url: ECHA page URL
            substance_name: Substance name for context
            cas_number: CAS number for context
            
        Returns:
            List of effect levels data dictionaries
        """
        logger.info(f"Scraping ECHA page: {url}")
        
        try:
            response = self.utils.make_request(url)
            if not response:
                logger.error(f"Failed to fetch ECHA page: {url}")
                return []
            
            soup = BeautifulSoup(response.text, 'lxml')
            
            # Extract effect levels data
            effect_levels_data = self._extract_effect_levels(soup, url)
            
            # Add context information to each record
            for record in effect_levels_data:
                record['source_url'] = url
                record['substance_name'] = substance_name
                record['cas_number'] = cas_number
                record['extraction_timestamp'] = time.time()
            
            logger.info(f"Extracted {len(effect_levels_data)} effect level records from {url}")
            return effect_levels_data
            
        except Exception as e:
            logger.error(f"Error scraping ECHA page {url}: {e}")
            return []
    
    def _extract_effect_levels(self, soup: BeautifulSoup, url: str) -> List[Dict[str, Any]]:
        """Extract effect levels data from ECHA page"""
        effect_levels_data = []
        
        try:
            # Strategy 1: Look for "Effect levels" section header
            effect_levels_section = self._find_effect_levels_section(soup)
            
            if effect_levels_section:
                # Extract table data from the section
                tables = effect_levels_section.find_all('table')
                for table in tables:
                    table_data = self._extract_table_data(table)
                    effect_levels_data.extend(table_data)
            
            # Strategy 2: Look for "Results and discussion" section
            if not effect_levels_data:
                results_section = self._find_results_discussion_section(soup)
                if results_section:
                    tables = results_section.find_all('table')
                    for table in tables:
                        table_data = self._extract_table_data(table)
                        effect_levels_data.extend(table_data)
            
            # Strategy 3: Look for any tables with toxicity-related headers
            if not effect_levels_data:
                effect_levels_data = self._find_toxicity_tables(soup)
            
            # Strategy 4: Extract key-value pairs from structured data
            if not effect_levels_data:
                effect_levels_data = self._extract_structured_data(soup)
            
        except Exception as e:
            logger.error(f"Error extracting effect levels from {url}: {e}")
        
        return effect_levels_data
    
    def _find_effect_levels_section(self, soup: BeautifulSoup) -> Optional[Tag]:
        """Find the Effect levels section in the page"""
        for keyword in config.EFFECT_LEVELS_KEYWORDS:
            # Look for headers containing the keyword
            headers = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            for header in headers:
                if keyword.lower() in header.get_text().lower():
                    # Return the parent section or next sibling
                    section = header.find_parent(['div', 'section', 'article'])
                    if section:
                        return section
                    
                    # Try to find the next content section
                    next_element = header.find_next_sibling()
                    if next_element:
                        return next_element
        
        return None
    
    def _find_results_discussion_section(self, soup: BeautifulSoup) -> Optional[Tag]:
        """Find Results and discussion section"""
        # Look for "Results and discussion" text
        results_text = soup.find(text=lambda text: text and "results and discussion" in text.lower())
        if results_text:
            # Find the parent container
            parent = results_text.find_parent(['div', 'section', 'article', 'td'])
            if parent:
                return parent
        
        return None
    
    def _find_toxicity_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Find tables that might contain toxicity data"""
        toxicity_data = []
        
        # Keywords that might indicate toxicity tables
        toxicity_keywords = [
            'effect', 'dose', 'concentration', 'ld50', 'lc50', 'noael', 'loael',
            'toxicity', 'endpoint', 'species', 'route', 'duration'
        ]
        
        tables = soup.find_all('table')
        for table in tables:
            table_text = table.get_text().lower()
            
            # Check if table contains toxicity-related keywords
            if any(keyword in table_text for keyword in toxicity_keywords):
                table_data = self._extract_table_data(table)
                toxicity_data.extend(table_data)
        
        return toxicity_data
    
    def _extract_table_data(self, table: Tag) -> List[Dict[str, Any]]:
        """Extract data from a table"""
        table_data = []
        
        try:
            # Get table headers
            headers = []
            header_row = table.find('thead')
            if header_row:
                headers = [clean_text(th.get_text()) for th in header_row.find_all(['th', 'td'])]
            else:
                # Try first row as headers
                first_row = table.find('tr')
                if first_row:
                    headers = [clean_text(cell.get_text()) for cell in first_row.find_all(['th', 'td'])]
            
            if not headers:
                logger.warning("No headers found in table")
                return []
            
            # Get table body
            tbody = table.find('tbody')
            rows = tbody.find_all('tr') if tbody else table.find_all('tr')[1:]  # Skip header row
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= len(headers):
                    row_data = {}
                    for i, cell in enumerate(cells[:len(headers)]):
                        if i < len(headers):
                            row_data[headers[i]] = clean_text(cell.get_text())
                    
                    # Only add non-empty rows
                    if any(value.strip() for value in row_data.values()):
                        table_data.append(row_data)
            
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
        
        return table_data
    
    def _extract_structured_data(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract structured data from definition lists or key-value pairs"""
        structured_data = []
        
        try:
            # Look for definition lists
            dl_elements = soup.find_all('dl')
            for dl in dl_elements:
                data_dict = {}
                terms = dl.find_all('dt')
                definitions = dl.find_all('dd')
                
                for term, definition in zip(terms, definitions):
                    key = clean_text(term.get_text())
                    value = clean_text(definition.get_text())
                    if key and value:
                        data_dict[key] = value
                
                if data_dict:
                    structured_data.append(data_dict)
            
            # Look for labeled data patterns
            labels = soup.find_all(text=lambda text: text and ':' in text)
            for label_text in labels:
                if any(keyword in label_text.lower() for keyword in ['effect', 'dose', 'concentration']):
                    parent = label_text.find_parent()
                    if parent:
                        # Try to extract key-value pair
                        parts = label_text.split(':', 1)
                        if len(parts) == 2:
                            key = clean_text(parts[0])
                            value = clean_text(parts[1])
                            if key and value:
                                structured_data.append({key: value})
        
        except Exception as e:
            logger.error(f"Error extracting structured data: {e}")
        
        return structured_data
    
    def validate_effect_levels_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean effect levels data"""
        validated_data = []
        
        for record in data:
            # Check if record contains meaningful toxicity data
            if self._is_valid_toxicity_record(record):
                # Clean and standardize the record
                cleaned_record = self._clean_toxicity_record(record)
                validated_data.append(cleaned_record)
        
        return validated_data
    
    def _is_valid_toxicity_record(self, record: Dict[str, Any]) -> bool:
        """Check if record contains valid toxicity data"""
        # Keywords that indicate valid toxicity data
        toxicity_indicators = [
            'mg/kg', 'mg/l', 'ppm', 'ld50', 'lc50', 'noael', 'loael',
            'effect', 'dose', 'concentration', 'mortality', 'toxic'
        ]
        
        record_text = ' '.join(str(value).lower() for value in record.values())
        return any(indicator in record_text for indicator in toxicity_indicators)
    
    def _clean_toxicity_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and standardize toxicity record"""
        cleaned_record = {}
        
        for key, value in record.items():
            # Standardize key names
            cleaned_key = self._standardize_key_name(key)
            cleaned_value = clean_text(str(value))
            
            if cleaned_key and cleaned_value:
                cleaned_record[cleaned_key] = cleaned_value
        
        return cleaned_record
    
    def _standardize_key_name(self, key: str) -> str:
        """Standardize key names for consistency"""
        key = clean_text(key).lower()
        
        # Map common variations to standard names
        key_mappings = {
            'effect level': 'effect_level',
            'dose level': 'dose_level',
            'concentration': 'concentration',
            'species': 'species',
            'route of administration': 'route',
            'duration': 'duration',
            'endpoint': 'endpoint',
            'reliability': 'reliability',
            'study type': 'study_type'
        }
        
        for pattern, standard in key_mappings.items():
            if pattern in key:
                return standard
        
        # Return cleaned key if no mapping found
        return key.replace(' ', '_').replace('-', '_')
