import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 配置 ---
INPUT_CSV_PATH = 'eChemportal-473.csv'  # 输入CSV文件名
OUTPUT_CSV_PATH = 'eChemPortal_with_labels.csv'  # 输出CSV文件名
LINK_COLUMN_NAME = 'Endpoint Link'  # URL列名

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def extract_info_from_page(soup):
    """从解析后的HTML中提取遗传毒性标签信息"""
    data = {
        'Genotoxicity': 'Not Found'
    }

    try:
        # 在整个页面中搜索包含"Genotoxicity"的th元素（表格结构）
        genotox_pattern = re.compile(r'genotoxicity', re.IGNORECASE)

        # 查找所有包含genotoxicity的文本节点
        for element in soup.find_all(string=genotox_pattern):
            parent = element.parent

            # 如果找到的是"Genotoxicity"标签（精确匹配）
            if element.strip().lower() == 'genotoxicity' and parent and parent.name == 'th':
                # 查找同一行的下一个th或td
                next_cell = parent.find_next_sibling(['th', 'td'])
                if next_cell:
                    # 如果第一个值是冒号，继续查找下一个
                    if next_cell.get_text(strip=True) == ':':
                        next_next_cell = next_cell.find_next_sibling(['th', 'td'])
                        if next_next_cell:
                            genotox_value = next_next_cell.get_text(strip=True)
                            data['Genotoxicity'] = genotox_value
                            logging.info(f"找到Genotoxicity标签: {genotox_value}")
                            break
                    else:
                        genotox_value = next_cell.get_text(strip=True)
                        data['Genotoxicity'] = genotox_value
                        logging.info(f"找到Genotoxicity标签: {genotox_value}")
                        break

        # 如果还是没找到，尝试查找dt/dd结构（备用方案）
        if data['Genotoxicity'] == 'Not Found':
            all_dts = soup.find_all('dt')
            for dt in all_dts:
                if dt.get_text(strip=True).lower() == 'genotoxicity':
                    dd = dt.find_next_sibling('dd')
                    if dd:
                        data['Genotoxicity'] = dd.get_text(strip=True)
                        logging.info(f"通过dt/dd结构找到Genotoxicity标签: {data['Genotoxicity']}")
                        break

        logging.info(f"提取的数据: {data}")

    except Exception as e:
        logging.error(f"解析页面时出错: {e}")

    return data

def scrape_single_url(url, retry_count=3):
    """爬取单个URL的信息，支持重试机制"""
    for attempt in range(retry_count):
        try:
            response = requests.get(url, headers=HEADERS, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            extracted_data = extract_info_from_page(soup)
            
            logging.info(f"成功提取数据: {extracted_data}")
            return extracted_data
            
        except requests.exceptions.RequestException as e:
            logging.warning(f"第{attempt + 1}次尝试失败: {e}")
            if attempt < retry_count - 1:
                time.sleep(2)  # 重试前等待
            else:
                logging.error(f"所有重试失败，URL: {url}")
                return {
                    'Genotoxicity': 'Request Error'
                }

def main():
    """主函数"""
    try:
        # 读取CSV文件
        df = pd.read_csv(INPUT_CSV_PATH)
        logging.info(f"成功读取文件，共{len(df)}行数据")
        
        if LINK_COLUMN_NAME not in df.columns:
            logging.error(f"文件中未找到'{LINK_COLUMN_NAME}'列")
            return
            
    except FileNotFoundError:
        logging.error(f"找不到文件'{INPUT_CSV_PATH}'")
        return
    except Exception as e:
        logging.error(f"读取文件时出错: {e}")
        return
    
    results_list = []
    total_links = len(df)
    
    logging.info(f"开始处理{total_links}个链接...")
    
    # 遍历每个URL
    for index, row in df.iterrows():
        url = row[LINK_COLUMN_NAME]
        logging.info(f"处理第{index + 1}/{total_links}个链接: {url}")
        
        # 检查URL是否有效
        if pd.isna(url) or url == '':
            logging.warning("空URL，跳过")
            results_list.append({
                'Genotoxicity': 'Empty URL'
            })
            continue
            
        # 爬取数据
        extracted_data = scrape_single_url(url)
        results_list.append(extracted_data)
        
        # 礼貌性暂停，避免给服务器造成压力
        time.sleep(1.5)
    
    logging.info("所有链接处理完毕，正在保存结果...")
    
    # 创建结果DataFrame
    results_df = pd.DataFrame(results_list)
    
    # 重命名列以区分原始数据和爬取数据
    results_df.rename(columns={
        'Genotoxicity': 'Scraped_Genotoxicity'
    }, inplace=True)
    
    # 合并数据
    final_df = pd.concat([df, results_df], axis=1)
    
    # 保存结果
    final_df.to_csv(OUTPUT_CSV_PATH, index=False, encoding='utf-8-sig')
    
    logging.info(f"任务完成！结果已保存到'{OUTPUT_CSV_PATH}'")
    
    # 统计结果
    success_count = sum(1 for _, row in results_df.iterrows()
                       if row['Scraped_Genotoxicity'] not in ['Not Found', 'Request Error', 'Empty URL'])
    logging.info(f"成功提取Genotoxicity标签的链接数: {success_count}/{total_links}")

    # 统计阳性和阴性结果
    positive_count = sum(1 for _, row in results_df.iterrows()
                        if row['Scraped_Genotoxicity'].lower() == 'positive')
    negative_count = sum(1 for _, row in results_df.iterrows()
                        if row['Scraped_Genotoxicity'].lower() == 'negative')
    logging.info(f"阳性结果: {positive_count}, 阴性结果: {negative_count}")

if __name__ == '__main__':
    main()