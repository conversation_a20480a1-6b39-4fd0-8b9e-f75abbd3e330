import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 配置 ---
INPUT_CSV_PATH = 'eChemPortal.xlsx'  # 输入CSV文件名
OUTPUT_CSV_PATH = 'eChemPortal_html.csv'  # 输出CSV文件名
LINK_COLUMN_NAME = 'Link'  # URL列名

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def extract_info_from_page(soup):
    """从解析后的HTML中提取所需信息"""
    data = {
        'Duration': 'Not Found',
        'Dose descriptor': 'Not Found', 
        'Effect conc.': 'Not Found'
    }
    
    try:
        # 查找"Results and discussion"标题 - 修改为h3标签
        results_header = soup.find('h3', string=re.compile(r'\s*Results and discussion\s*', re.IGNORECASE))
        
        if not results_header:
            # 如果没找到h3，也尝试查找id为"sResultsAndDiscussion"的元素
            results_header = soup.find('h3', id='sResultsAndDiscussion')
            
        if not results_header:
            logging.warning("未找到'Results and discussion'标题")
            return data
            
        # 查找紧跟在标题后的内容 - 可能是div或直接的兄弟元素
        content_div = results_header.find_next_sibling('div')
        
        if not content_div:
            # 如果没有找到div，尝试查找下一个包含dl的元素
            content_div = results_header.find_next('div', class_='sBlock')
            
        if not content_div:
            # 如果还是没找到，直接在results_header后查找所有dl元素
            content_div = results_header.parent
            
        if not content_div:
            logging.warning("未找到内容容器")
            return data
            
        # 查找描述列表中的数据 - 在content_div及其后续元素中查找
        dts = content_div.find_all('dt')
        
        # 如果在当前容器中没找到，扩大搜索范围
        if not dts:
            # 从results_header开始，查找后续所有的dt元素
            current_element = results_header
            while current_element and not dts:
                current_element = current_element.find_next_sibling()
                if current_element:
                    dts = current_element.find_all('dt')
        
        for dt in dts:
            key = dt.get_text(strip=True)
            dd = dt.find_next_sibling('dd')
            
            if dd:
                value = dd.get_text(strip=True)
                
                # 更精确的匹配逻辑
                if key.lower().startswith('duration'):
                    data['Duration'] = value
                elif key.lower().startswith('dose descriptor'):
                    data['Dose descriptor'] = value
                elif key.lower().startswith('effect conc'):
                    data['Effect conc.'] = value
                    
        logging.info(f"提取的原始数据: {data}")
                    
    except Exception as e:
        logging.error(f"解析页面时出错: {e}")
        
    return data

def scrape_single_url(url, retry_count=3):
    """爬取单个URL的信息，支持重试机制"""
    for attempt in range(retry_count):
        try:
            response = requests.get(url, headers=HEADERS, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'lxml')
            extracted_data = extract_info_from_page(soup)
            
            logging.info(f"成功提取数据: {extracted_data}")
            return extracted_data
            
        except requests.exceptions.RequestException as e:
            logging.warning(f"第{attempt + 1}次尝试失败: {e}")
            if attempt < retry_count - 1:
                time.sleep(2)  # 重试前等待
            else:
                logging.error(f"所有重试失败，URL: {url}")
                return {
                    'Duration': 'Request Error', 
                    'Dose descriptor': 'Request Error', 
                    'Effect conc.': 'Request Error'
                }

def main():
    """主函数"""
    try:
        # 读取CSV文件
        df = pd.read_excel(INPUT_CSV_PATH)
        logging.info(f"成功读取文件，共{len(df)}行数据")
        
        if LINK_COLUMN_NAME not in df.columns:
            logging.error(f"文件中未找到'{LINK_COLUMN_NAME}'列")
            return
            
    except FileNotFoundError:
        logging.error(f"找不到文件'{INPUT_CSV_PATH}'")
        return
    except Exception as e:
        logging.error(f"读取文件时出错: {e}")
        return
    
    results_list = []
    total_links = len(df)
    
    logging.info(f"开始处理{total_links}个链接...")
    
    # 遍历每个URL
    for index, row in df.iterrows():
        url = row[LINK_COLUMN_NAME]
        logging.info(f"处理第{index + 1}/{total_links}个链接: {url}")
        
        # 检查URL是否有效
        if pd.isna(url) or url == '':
            logging.warning("空URL，跳过")
            results_list.append({
                'Duration': 'Empty URL', 
                'Dose descriptor': 'Empty URL', 
                'Effect conc.': 'Empty URL'
            })
            continue
            
        # 爬取数据
        extracted_data = scrape_single_url(url)
        results_list.append(extracted_data)
        
        # 礼貌性暂停，避免给服务器造成压力
        time.sleep(1.5)
    
    logging.info("所有链接处理完毕，正在保存结果...")
    
    # 创建结果DataFrame
    results_df = pd.DataFrame(results_list)
    
    # 重命名列以区分原始数据和爬取数据
    results_df.rename(columns={
        'Duration': 'Scraped_Duration',
        'Dose descriptor': 'Scraped_Dose_descriptor', 
        'Effect conc.': 'Scraped_Effect_conc'
    }, inplace=True)
    
    # 合并数据
    final_df = pd.concat([df, results_df], axis=1)
    
    # 保存结果
    final_df.to_csv(OUTPUT_CSV_PATH, index=False, encoding='utf-8-sig')
    
    logging.info(f"任务完成！结果已保存到'{OUTPUT_CSV_PATH}'")
    
    # 统计结果
    success_count = sum(1 for _, row in results_df.iterrows() 
                       if row['Scraped_Duration'] not in ['Not Found', 'Request Error', 'Empty URL'])
    logging.info(f"成功提取数据的链接数: {success_count}/{total_links}")

if __name__ == '__main__':
    main()