import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def extract_info_from_page(soup):
    """从解析后的HTML中提取遗传毒性标签信息"""
    data = {
        'Genotoxicity': 'Not Found'
    }
    
    try:
        # 在整个页面中搜索包含"Genotoxicity"的th元素（表格结构）
        genotox_pattern = re.compile(r'genotoxicity', re.IGNORECASE)
        
        # 查找所有包含genotoxicity的文本节点
        for element in soup.find_all(string=genotox_pattern):
            parent = element.parent
            
            # 如果找到的是"Genotoxicity"标签（精确匹配）
            if element.strip().lower() == 'genotoxicity' and parent and parent.name == 'th':
                # 查找同一行的下一个th或td
                next_cell = parent.find_next_sibling(['th', 'td'])
                if next_cell:
                    # 如果第一个值是冒号，继续查找下一个
                    if next_cell.get_text(strip=True) == ':':
                        next_next_cell = next_cell.find_next_sibling(['th', 'td'])
                        if next_next_cell:
                            genotox_value = next_next_cell.get_text(strip=True)
                            data['Genotoxicity'] = genotox_value
                            logging.info(f"找到Genotoxicity标签: {genotox_value}")
                            break
                    else:
                        genotox_value = next_cell.get_text(strip=True)
                        data['Genotoxicity'] = genotox_value
                        logging.info(f"找到Genotoxicity标签: {genotox_value}")
                        break
        
        # 如果还是没找到，尝试查找dt/dd结构（备用方案）
        if data['Genotoxicity'] == 'Not Found':
            all_dts = soup.find_all('dt')
            for dt in all_dts:
                if dt.get_text(strip=True).lower() == 'genotoxicity':
                    dd = dt.find_next_sibling('dd')
                    if dd:
                        data['Genotoxicity'] = dd.get_text(strip=True)
                        logging.info(f"通过dt/dd结构找到Genotoxicity标签: {data['Genotoxicity']}")
                        break
                    
        logging.info(f"提取的数据: {data}")
                    
    except Exception as e:
        logging.error(f"解析页面时出错: {e}")
        
    return data

def test_single_url(url):
    """测试单个URL"""
    try:
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'lxml')
        extracted_data = extract_info_from_page(soup)
        
        return extracted_data
        
    except Exception as e:
        logging.error(f"请求失败: {e}")
        return {'Genotoxicity': 'Request Error'}

# 测试几个URL
test_urls = [
    ("https://www.nite.go.jp/chem/jcheck//direct.action?TYPE=DPAGE2&CAS=1314-98-3&MITI=1-572&ANO=26749", "positive"),  # 应该是positive
    ("https://www.nite.go.jp/chem/jcheck//direct.action?TYPE=DPAGE2&CAS=112-18-5&MITI=2-176&ANO=26738", "negative"),  # 应该是negative
    ("https://www.nite.go.jp/chem/jcheck//direct.action?TYPE=DPAGE2&CAS=3710-84-7&MITI=2-190&ANO=26752", "positive")   # 应该是positive
]

print("开始测试修改后的主要爬虫代码...")
for i, (url, expected) in enumerate(test_urls, 1):
    print(f"\n测试URL {i}: {url}")
    print(f"期望结果: {expected}")
    result = test_single_url(url)
    print(f"实际结果: {result}")
    
    # 验证结果
    if result['Genotoxicity'].lower() == expected.lower():
        print("✅ 测试通过")
    else:
        print("❌ 测试失败")
    
    time.sleep(2)  # 礼貌性暂停

print("\n测试完成！")
