"""
Test script for the eChemPortal scraper
Validates setup and performs basic functionality tests
"""
import sys
import time
from pathlib import Path
from loguru import logger

# Test imports
try:
    import requests
    import pandas as pd
    from bs4 import BeautifulSoup
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    print("✓ All required packages imported successfully")
except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Please install required packages: pip install -r requirements.txt")
    sys.exit(1)

# Test local modules
try:
    import config
    from utils import ScrapingUtils, ProgressManager, setup_logging
    from level1_scraper import Level1Scraper
    from level2_scraper import Level2Scraper
    from data_processor import DataProcessor
    from error_handler import error_handler
    print("✓ All local modules imported successfully")
except ImportError as e:
    print(f"✗ Local module import error: {e}")
    sys.exit(1)


def test_configuration():
    """Test configuration settings"""
    print("\n" + "="*50)
    print("TESTING CONFIGURATION")
    print("="*50)
    
    # Test directory creation
    for directory in [config.DATA_DIR, config.OUTPUT_DIR, config.LOGS_DIR]:
        if directory.exists():
            print(f"✓ Directory exists: {directory}")
        else:
            print(f"✗ Directory missing: {directory}")
    
    # Test configuration values
    print(f"✓ Base URL: {config.ECHEMPORTAL_BASE_URL}")
    print(f"✓ Request timeout: {config.REQUEST_TIMEOUT}s")
    print(f"✓ Max retries: {config.MAX_RETRIES}")
    print(f"✓ Target property: {config.TARGET_PROPERTY}")


def test_utilities():
    """Test utility functions"""
    print("\n" + "="*50)
    print("TESTING UTILITIES")
    print("="*50)
    
    # Test ScrapingUtils
    utils = ScrapingUtils()
    user_agent = utils.get_random_user_agent()
    print(f"✓ Random user agent: {user_agent[:50]}...")
    
    # Test ProgressManager
    progress = ProgressManager()
    progress.update_page(5)
    progress.add_processed_substance("test_substance")
    print(f"✓ Progress manager: Last page = {progress.get_last_page()}")
    
    # Test logging setup
    setup_logging()
    logger.info("Test log message")
    print("✓ Logging setup completed")


def test_selenium_setup():
    """Test Selenium WebDriver setup"""
    print("\n" + "="*50)
    print("TESTING SELENIUM SETUP")
    print("="*50)
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("https://www.google.com")
        
        if "Google" in driver.title:
            print("✓ Selenium WebDriver working correctly")
        else:
            print("✗ Selenium WebDriver test failed")
        
        driver.quit()
        
    except Exception as e:
        print(f"✗ Selenium setup error: {e}")
        print("Please ensure ChromeDriver is installed and in PATH")


def test_network_connectivity():
    """Test network connectivity to target sites"""
    print("\n" + "="*50)
    print("TESTING NETWORK CONNECTIVITY")
    print("="*50)
    
    utils = ScrapingUtils()
    
    # Test eChemPortal
    try:
        response = utils.make_request(config.ECHEMPORTAL_BASE_URL)
        if response and response.status_code == 200:
            print("✓ eChemPortal accessible")
        else:
            print("✗ eChemPortal not accessible")
    except Exception as e:
        print(f"✗ eChemPortal connection error: {e}")
    
    # Test ECHA
    try:
        response = utils.make_request(config.ECHA_BASE_URL)
        if response and response.status_code == 200:
            print("✓ ECHA accessible")
        else:
            print("✗ ECHA not accessible")
    except Exception as e:
        print(f"✗ ECHA connection error: {e}")


def test_data_processing():
    """Test data processing functionality"""
    print("\n" + "="*50)
    print("TESTING DATA PROCESSING")
    print("="*50)
    
    processor = DataProcessor()
    
    # Test data with sample records
    level1_data = {
        'substance_name': 'Test Chemical',
        'cas_number': '123-45-6',
        'source': 'ECHA REACH',
        'echa_url': 'https://example.com',
        'has_acute_toxicity_data': True
    }
    
    level2_data = [{
        'effect_level': 'LD50',
        'dose_level': '500 mg/kg',
        'species': 'rat',
        'route': 'oral'
    }]
    
    processed_records = processor.process_substance_data(level1_data, level2_data)
    
    if processed_records:
        print(f"✓ Data processing successful: {len(processed_records)} records")
        print(f"  Sample record keys: {list(processed_records[0].keys())[:5]}...")
    else:
        print("✗ Data processing failed")


def test_error_handling():
    """Test error handling functionality"""
    print("\n" + "="*50)
    print("TESTING ERROR HANDLING")
    print("="*50)
    
    # Test error logging
    try:
        raise ValueError("Test error")
    except Exception as e:
        error_handler.log_error(
            error_type=error_handler.ErrorType.DATA_VALIDATION_ERROR,
            error_message=str(e),
            context={'test': 'error_handling'},
            exception=e
        )
    
    summary = error_handler.get_error_summary()
    print(f"✓ Error handling: {summary['total_errors']} errors logged")
    
    # Test progress monitoring
    error_handler.progress_monitor.update_progress(
        current_page=1,
        total_pages=10,
        processed_substances=5
    )
    
    stats = error_handler.progress_monitor.get_progress_stats()
    print(f"✓ Progress monitoring: {stats['processed_substances']} substances processed")


def run_basic_scraping_test():
    """Run a basic scraping test (limited scope)"""
    print("\n" + "="*50)
    print("RUNNING BASIC SCRAPING TEST")
    print("="*50)
    
    print("Note: This is a limited test. Full scraping requires manual verification.")
    print("The test will attempt to:")
    print("1. Initialize Level 1 scraper")
    print("2. Navigate to eChemPortal (without performing actual search)")
    print("3. Test Level 2 scraper with a sample URL")
    
    try:
        # Test Level 1 scraper initialization
        with Level1Scraper() as scraper:
            print("✓ Level 1 scraper initialized successfully")
            
            # Note: We don't perform actual navigation to avoid hitting the server
            print("✓ Level 1 scraper ready for use")
        
        # Test Level 2 scraper
        level2_scraper = Level2Scraper()
        print("✓ Level 2 scraper initialized successfully")
        
        print("✓ Basic scraping test completed")
        
    except Exception as e:
        print(f"✗ Basic scraping test failed: {e}")


def main():
    """Run all tests"""
    print("eChemPortal Scraper - Test Suite")
    print("="*60)
    
    test_configuration()
    test_utilities()
    test_selenium_setup()
    test_network_connectivity()
    test_data_processing()
    test_error_handling()
    run_basic_scraping_test()
    
    print("\n" + "="*60)
    print("TEST SUITE COMPLETED")
    print("="*60)
    print("\nIf all tests passed, you can run the main scraper with:")
    print("python main.py")
    print("\nFor a limited test run, modify config.py to set:")
    print("MAX_PAGES_TO_SCRAPE = 2")


if __name__ == "__main__":
    main()
