"""
Setup script for eChemPortal Chemical Data Scraper
Helps with installation and initial configuration
"""
import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    else:
        print(f"✅ Python version: {sys.version}")
        return True


def install_requirements():
    """Install required packages"""
    print("\n📦 Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False


def check_chromedriver():
    """Check if ChromeDriver is available"""
    print("\n🌐 Checking ChromeDriver...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        
        print("✅ ChromeDriver is working correctly")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver issue: {e}")
        print("\n📋 ChromeDriver Installation Instructions:")
        print("1. Download ChromeDriver from: https://chromedriver.chromium.org/")
        print("2. Make sure it matches your Chrome browser version")
        print("3. Add ChromeDriver to your system PATH, or")
        print("4. Place chromedriver.exe in this project directory")
        return False


def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = ["data", "output", "logs"]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ Created/verified directory: {dir_name}")


def create_sample_config():
    """Create a sample configuration file for testing"""
    print("\n⚙️ Creating sample configuration...")
    
    sample_config_path = Path("config_sample.py")
    
    if not sample_config_path.exists():
        sample_content = '''"""
Sample configuration for testing
Copy this to config.py and modify as needed
"""
from pathlib import Path

# Base URLs
ECHEMPORTAL_BASE_URL = "https://echemportal.org/echemportal/property-search"
ECHA_BASE_URL = "https://echa.europa.eu"

# Request settings
REQUEST_TIMEOUT = 30
MAX_RETRIES = 3
RETRY_DELAY = 5

# Rate limiting (be respectful!)
DELAY_BETWEEN_REQUESTS = 3  # Increased for testing
DELAY_BETWEEN_PAGES = 5     # Increased for testing
DELAY_BETWEEN_ECHA_REQUESTS = 2

# For testing - limit the number of pages
MAX_PAGES_TO_SCRAPE = 2  # Set to None for full scraping

# Other settings remain the same as config.py
# ... (copy other settings from config.py)
'''
        
        with open(sample_config_path, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        print("✅ Created config_sample.py")
        print("   Modify this file for your specific needs")
    else:
        print("✅ config_sample.py already exists")


def run_basic_tests():
    """Run basic functionality tests"""
    print("\n🧪 Running basic tests...")
    
    try:
        # Test imports
        import requests
        import pandas as pd
        from bs4 import BeautifulSoup
        print("✅ Core packages import successfully")
        
        # Test network connectivity
        response = requests.get("https://www.google.com", timeout=10)
        if response.status_code == 200:
            print("✅ Network connectivity working")
        else:
            print("⚠️ Network connectivity issue")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic tests failed: {e}")
        return False


def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETED!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("1. Run tests to verify everything works:")
    print("   python test_scraper.py")
    
    print("\n2. For a limited test run, modify config.py:")
    print("   MAX_PAGES_TO_SCRAPE = 2")
    
    print("\n3. Run the main scraper:")
    print("   python main.py")
    
    print("\n⚠️ Important Notes:")
    print("• Be respectful to the target websites")
    print("• Monitor the scraping process and error logs")
    print("• The scraper will create progress files to resume if interrupted")
    print("• Check output/ directory for results")
    
    print("\n📁 Project Structure:")
    print("├── main.py              # Main entry point")
    print("├── test_scraper.py      # Test script")
    print("├── config.py            # Configuration")
    print("├── level1_scraper.py    # eChemPortal scraper")
    print("├── level2_scraper.py    # ECHA scraper")
    print("├── data_processor.py    # Data processing")
    print("├── error_handler.py     # Error handling")
    print("├── utils.py             # Utilities")
    print("├── data/                # Progress and temp files")
    print("├── output/              # Final results")
    print("└── logs/                # Log files")


def main():
    """Main setup function"""
    print("🚀 eChemPortal Chemical Data Scraper - Setup")
    print("="*60)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Install requirements
    if success and not install_requirements():
        success = False
    
    # Check ChromeDriver
    if success:
        check_chromedriver()  # Don't fail setup if ChromeDriver has issues
    
    # Create directories
    create_directories()
    
    # Create sample config
    create_sample_config()
    
    # Run basic tests
    if success:
        run_basic_tests()
    
    # Print next steps
    print_next_steps()
    
    if success:
        print("\n✅ Setup completed successfully!")
    else:
        print("\n⚠️ Setup completed with some issues. Please resolve them before running the scraper.")


if __name__ == "__main__":
    main()
