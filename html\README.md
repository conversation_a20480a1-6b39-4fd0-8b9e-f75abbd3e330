# eChemPortal Chemical Data Scraper

A comprehensive web scraper for extracting chemical substance data from eChemPortal and ECHA databases, specifically targeting acute oral toxicity information.

## Overview

This project implements a two-level scraping approach:

1. **Level 1**: Scrapes eChemPortal property search results to extract basic chemical information and ECHA links
2. **Level 2**: Follows ECHA links to extract detailed "Effect levels" data from registration dossiers

## Features

- **Multi-stage scraping**: Combines Selenium for dynamic content and requests+BeautifulSoup for static parsing
- **Robust error handling**: Comprehensive retry mechanisms and error logging
- **Progress tracking**: Resume functionality for long-running scraping sessions
- **Rate limiting**: Respectful delays to avoid IP blocking
- **Data validation**: Clean and validate extracted data
- **Incremental storage**: Save progress periodically to prevent data loss

## Quick Start

### 1. Setup
```bash
# Run the setup script
python setup.py

# Or install manually:
pip install -r requirements.txt
```

### 2. Test Installation
```bash
python test_scraper.py
```

### 3. Run Scraper
```bash
# For testing (limited pages)
# Edit config.py: MAX_PAGES_TO_SCRAPE = 2
python main.py

# For full scraping
# Edit config.py: MAX_PAGES_TO_SCRAPE = None
python main.py
```

## Installation

### Automatic Setup
```bash
python setup.py
```

### Manual Setup
1. Install Python 3.8+
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Install ChromeDriver:
   - Download from https://chromedriver.chromium.org/
   - Ensure version matches your Chrome browser
   - Add to PATH or place in project directory

## Configuration

Edit `config.py` to customize:
- **Rate limiting**: `DELAY_BETWEEN_REQUESTS`, `DELAY_BETWEEN_PAGES`
- **Testing**: `MAX_PAGES_TO_SCRAPE = 2` (set to `None` for full scraping)
- **Output**: File locations and formats
- **Retry logic**: `MAX_RETRIES`, `RETRY_DELAY`

## Usage

### Basic Usage
```bash
python main.py
```

### Resume Scraping
The scraper automatically saves progress and can resume from the last processed page if interrupted.

## Project Structure

```
├── main.py                 # Main entry point
├── setup.py               # Setup and installation script
├── test_scraper.py        # Test and validation script
├── config.py              # Configuration settings
├── utils.py               # Utility functions
├── level1_scraper.py      # eChemPortal scraper
├── level2_scraper.py      # ECHA detail page scraper
├── data_processor.py      # Data processing and storage
├── error_handler.py       # Error handling and monitoring
├── requirements.txt       # Python dependencies
├── data/                  # Progress and temporary data
├── output/               # Final output files
└── logs/                 # Log files
```

## Workflow

### 1. Level 1 Scraping (eChemPortal)
- Navigate through property search result pages
- Extract substance names, CAS numbers
- Find "Acute toxicity: oral" links to ECHA
- Filter by "ECHA REACH" source

### 2. Level 2 Scraping (ECHA)
- Follow ECHA registration dossier links
- Extract "Effect levels" table data
- Parse Results and discussion sections
- Collect detailed toxicity information

### 3. Data Processing
- Integrate Level 1 and Level 2 data
- Clean and validate extracted information
- Remove duplicates and standardize formats
- Save incrementally to prevent data loss

## Output

The scraper generates:
- `output/echemportal_acute_toxicity_data.csv`: Main results file
- `data/scraping_progress.json`: Progress tracking for resume functionality
- `logs/errors.log`: Detailed error logs
- `logs/processing_errors.json`: Data processing error details

## Data Fields

Extracted data includes:
- **Basic Info**: Substance Name (IUPAC), CAS Number, Source
- **Effect Levels**: Dose levels, concentrations, effect values
- **Study Details**: Species, route of administration, duration
- **Quality Info**: Reliability scores, study types, endpoints
- **Metadata**: Extraction timestamps, data completeness flags

## Monitoring and Progress

The scraper provides:
- **Real-time progress**: Page counts, success rates, time estimates
- **Error tracking**: Categorized errors with retry logic
- **Resume capability**: Automatic progress saving and restoration
- **Statistics**: Processing summaries and performance metrics

## Important Notes

- **Rate Limiting**: Respectful delays between requests (2-5 seconds)
- **User Agents**: Rotates user agents to avoid detection
- **Error Handling**: Continues scraping even if individual pages fail
- **Legal Compliance**: Ensure compliance with website terms of service
- **Resource Usage**: Monitor memory and network usage during long runs

## Troubleshooting

### Common Issues

1. **ChromeDriver Issues**
   ```bash
   # Check Chrome version
   google-chrome --version
   # Download matching ChromeDriver from https://chromedriver.chromium.org/
   ```

2. **Network Errors**
   - Check internet connection
   - Verify firewall settings
   - Try increasing delays in config.py

3. **Rate Limiting**
   ```python
   # In config.py, increase delays:
   DELAY_BETWEEN_REQUESTS = 5
   DELAY_BETWEEN_PAGES = 10
   ```

4. **Memory Issues**
   ```python
   # In config.py, reduce batch size:
   BATCH_SIZE = 25
   SAVE_PROGRESS_EVERY = 5
   ```

5. **Selenium Issues**
   - Ensure ChromeDriver is in PATH
   - Try running in non-headless mode for debugging
   - Check Chrome browser installation

### Debug Mode

For debugging, modify config.py:
```python
HEADLESS_MODE = False  # See browser actions
LOG_LEVEL = "DEBUG"    # Verbose logging
MAX_PAGES_TO_SCRAPE = 1  # Test with single page
```

## Performance Tips

- **Start Small**: Test with `MAX_PAGES_TO_SCRAPE = 2`
- **Monitor Progress**: Check logs and progress files regularly
- **Resume Capability**: Use Ctrl+C to stop gracefully and resume later
- **Batch Processing**: Adjust `SAVE_PROGRESS_EVERY` based on your needs

## Legal and Ethical Considerations

- **Respect robots.txt**: Check website policies
- **Rate Limiting**: Don't overwhelm servers
- **Data Usage**: Use scraped data responsibly
- **Terms of Service**: Comply with website terms
- **Academic/Research Use**: Ensure proper attribution

## License

This project is for educational and research purposes. Please respect the terms of service of the target websites and use the data responsibly.
