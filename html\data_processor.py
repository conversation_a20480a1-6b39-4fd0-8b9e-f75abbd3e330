"""
Data Processing and Storage Module
Handles data cleaning, validation, integration, and incremental storage
"""
import pandas as pd
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from loguru import logger
import hashlib

import config
from utils import clean_text, extract_cas_number


class DataProcessor:
    """Handle data processing, cleaning, and storage operations"""
    
    def __init__(self):
        self.processed_records = []
        self.duplicate_tracker = set()
        self.error_records = []
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        for directory in [config.DATA_DIR, config.OUTPUT_DIR, config.LOGS_DIR]:
            directory.mkdir(exist_ok=True)
    
    def process_substance_data(self, level1_data: Dict[str, Any], 
                             level2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process and integrate data from Level 1 and Level 2 scrapers
        
        Args:
            level1_data: Basic substance info from eChemPortal
            level2_data: Effect levels data from ECHA
            
        Returns:
            List of processed and integrated records
        """
        processed_records = []
        
        try:
            # If no Level 2 data, create a record with just Level 1 data
            if not level2_data:
                record = self._create_base_record(level1_data)
                record['data_completeness'] = 'level1_only'
                record['effect_levels_available'] = False
                processed_records.append(record)
            else:
                # Create integrated records for each effect level entry
                for effect_data in level2_data:
                    record = self._create_integrated_record(level1_data, effect_data)
                    if record and self._is_valid_record(record):
                        processed_records.append(record)
            
            # Remove duplicates
            processed_records = self._remove_duplicates(processed_records)
            
            logger.info(f"Processed {len(processed_records)} records for substance: {level1_data.get('substance_name', 'Unknown')}")
            
        except Exception as e:
            logger.error(f"Error processing substance data: {e}")
            self._log_error_record(level1_data, str(e))
        
        return processed_records
    
    def _create_base_record(self, level1_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create base record from Level 1 data"""
        return {
            # Basic substance information
            'substance_name': clean_text(level1_data.get('substance_name', '')),
            'cas_number': level1_data.get('cas_number', ''),
            'source': level1_data.get('source', ''),
            'echa_url': level1_data.get('echa_url', ''),
            'has_acute_toxicity_data': level1_data.get('has_acute_toxicity_data', False),
            
            # Processing metadata
            'extraction_timestamp': time.time(),
            'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'data_completeness': 'unknown',
            'effect_levels_available': False,
            
            # Placeholder fields for effect levels data
            'effect_level': '',
            'dose_level': '',
            'concentration': '',
            'species': '',
            'route': '',
            'duration': '',
            'endpoint': '',
            'reliability': '',
            'study_type': '',
            'additional_data': ''
        }
    
    def _create_integrated_record(self, level1_data: Dict[str, Any], 
                                effect_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create integrated record from Level 1 and Level 2 data"""
        try:
            record = self._create_base_record(level1_data)
            
            # Update with effect levels data
            record['data_completeness'] = 'complete'
            record['effect_levels_available'] = True
            
            # Map effect data fields to standardized fields
            field_mappings = {
                'effect_level': ['effect_level', 'effect level', 'effect', 'value'],
                'dose_level': ['dose_level', 'dose level', 'dose', 'dose_mg_kg'],
                'concentration': ['concentration', 'conc', 'mg/l', 'ppm'],
                'species': ['species', 'test species', 'animal'],
                'route': ['route', 'route of administration', 'administration route'],
                'duration': ['duration', 'exposure duration', 'time'],
                'endpoint': ['endpoint', 'end point', 'parameter'],
                'reliability': ['reliability', 'reliability score', 'quality'],
                'study_type': ['study_type', 'study type', 'type of study', 'method']
            }
            
            # Map fields from effect data
            for standard_field, possible_keys in field_mappings.items():
                value = self._extract_field_value(effect_data, possible_keys)
                if value:
                    record[standard_field] = value
            
            # Store any additional unmapped data
            additional_data = {}
            for key, value in effect_data.items():
                if not self._is_mapped_field(key, field_mappings) and value:
                    additional_data[key] = value
            
            if additional_data:
                record['additional_data'] = json.dumps(additional_data, ensure_ascii=False)
            
            return record
            
        except Exception as e:
            logger.error(f"Error creating integrated record: {e}")
            return None
    
    def _extract_field_value(self, data: Dict[str, Any], possible_keys: List[str]) -> str:
        """Extract field value using multiple possible key names"""
        for key in possible_keys:
            # Try exact match
            if key in data and data[key]:
                return clean_text(str(data[key]))
            
            # Try case-insensitive match
            for data_key, value in data.items():
                if key.lower() == data_key.lower() and value:
                    return clean_text(str(value))
            
            # Try partial match
            for data_key, value in data.items():
                if key.lower() in data_key.lower() and value:
                    return clean_text(str(value))
        
        return ''
    
    def _is_mapped_field(self, field_name: str, field_mappings: Dict[str, List[str]]) -> bool:
        """Check if field is already mapped to a standard field"""
        field_name_lower = field_name.lower()
        for possible_keys in field_mappings.values():
            if any(key.lower() == field_name_lower or key.lower() in field_name_lower 
                   for key in possible_keys):
                return True
        return False
    
    def _is_valid_record(self, record: Dict[str, Any]) -> bool:
        """Validate record quality and completeness"""
        # Must have substance name
        if not record.get('substance_name', '').strip():
            return False
        
        # Should have either CAS number or some effect data
        has_cas = bool(record.get('cas_number', '').strip())
        has_effect_data = any(record.get(field, '').strip() 
                            for field in ['effect_level', 'dose_level', 'concentration'])
        
        return has_cas or has_effect_data
    
    def _remove_duplicates(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate records based on content hash"""
        unique_records = []
        seen_hashes = set()
        
        for record in records:
            # Create hash based on key fields
            hash_content = f"{record.get('substance_name', '')}_" \
                          f"{record.get('cas_number', '')}_" \
                          f"{record.get('effect_level', '')}_" \
                          f"{record.get('dose_level', '')}"
            
            record_hash = hashlib.md5(hash_content.encode()).hexdigest()
            
            if record_hash not in seen_hashes:
                seen_hashes.add(record_hash)
                unique_records.append(record)
            else:
                logger.debug(f"Removed duplicate record for {record.get('substance_name', 'Unknown')}")
        
        return unique_records
    
    def _log_error_record(self, data: Dict[str, Any], error: str):
        """Log error record for debugging"""
        error_record = {
            'timestamp': time.time(),
            'data': data,
            'error': error
        }
        self.error_records.append(error_record)
    
    def save_data_incremental(self, records: List[Dict[str, Any]], 
                            append_mode: bool = True) -> bool:
        """Save data incrementally to CSV file"""
        try:
            if not records:
                logger.warning("No records to save")
                return True
            
            df = pd.DataFrame(records)
            
            # Clean and standardize DataFrame
            df = self._clean_dataframe(df)
            
            # Save to CSV
            if append_mode and config.OUTPUT_CSV_FILE.exists():
                # Append to existing file
                df.to_csv(config.OUTPUT_CSV_FILE, mode='a', header=False, 
                         index=False, encoding='utf-8-sig')
                logger.info(f"Appended {len(records)} records to {config.OUTPUT_CSV_FILE}")
            else:
                # Create new file or overwrite
                df.to_csv(config.OUTPUT_CSV_FILE, index=False, encoding='utf-8-sig')
                logger.info(f"Saved {len(records)} records to {config.OUTPUT_CSV_FILE}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving data: {e}")
            return False
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize DataFrame"""
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Clean text fields
        text_columns = ['substance_name', 'effect_level', 'dose_level', 
                       'concentration', 'species', 'route', 'duration', 
                       'endpoint', 'reliability', 'study_type']
        
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).apply(lambda x: clean_text(x) if pd.notna(x) else '')
        
        # Standardize CAS numbers
        if 'cas_number' in df.columns:
            df['cas_number'] = df['cas_number'].apply(self._standardize_cas_number)
        
        # Sort by substance name and CAS number
        sort_columns = ['substance_name']
        if 'cas_number' in df.columns:
            sort_columns.append('cas_number')
        
        df = df.sort_values(sort_columns)
        
        return df
    
    def _standardize_cas_number(self, cas: str) -> str:
        """Standardize CAS number format"""
        if pd.isna(cas) or not cas:
            return ''
        
        cas_str = str(cas).strip()
        extracted_cas = extract_cas_number(cas_str)
        return extracted_cas if extracted_cas else cas_str
    
    def save_error_log(self):
        """Save error records to log file"""
        if not self.error_records:
            return
        
        try:
            error_log_path = config.LOGS_DIR / "processing_errors.json"
            with open(error_log_path, 'w', encoding='utf-8') as f:
                json.dump(self.error_records, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Saved {len(self.error_records)} error records to {error_log_path}")
            
        except Exception as e:
            logger.error(f"Error saving error log: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            'total_processed_records': len(self.processed_records),
            'total_error_records': len(self.error_records),
            'unique_substances': len(set(r.get('substance_name', '') for r in self.processed_records)),
            'records_with_effect_data': len([r for r in self.processed_records if r.get('effect_levels_available')]),
            'processing_timestamp': time.time()
        }
