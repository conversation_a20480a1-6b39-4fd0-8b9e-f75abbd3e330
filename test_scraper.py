import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def extract_info_from_page(soup):
    """从解析后的HTML中提取遗传毒性标签信息"""
    data = {
        'Genotoxicity': 'Not Found'
    }
    
    try:
        # 查找"RESULTS AND DISCUSSION"标题
        results_header = soup.find('h3', string=re.compile(r'\s*RESULTS AND DISCUSSION\s*', re.IGNORECASE))
        
        if not results_header:
            logging.warning("未找到'RESULTS AND DISCUSSION'标题")
            return data
            
        # 在RESULTS AND DISCUSSION部分查找所有的dt元素
        current_element = results_header.find_next_sibling()
        
        # 遍历RESULTS AND DISCUSSION部分的所有内容
        while current_element:
            # 如果遇到下一个h3标题，说明已经超出了RESULTS AND DISCUSSION部分
            if current_element.name == 'h3':
                break
                
            # 查找当前元素中的所有dt标签
            if current_element.name:
                dts = current_element.find_all('dt')
                
                for dt in dts:
                    key = dt.get_text(strip=True)
                    dd = dt.find_next_sibling('dd')
                    
                    if dd:
                        value = dd.get_text(strip=True)
                        
                        # 查找Genotoxicity标签
                        if key.lower() == 'genotoxicity':
                            data['Genotoxicity'] = value
                            logging.info(f"找到Genotoxicity标签: {value}")
                            break
                            
            current_element = current_element.find_next_sibling()
            
        # 如果在上面的方法中没找到，尝试更广泛的搜索
        if data['Genotoxicity'] == 'Not Found':
            # 在整个页面中搜索包含"Genotoxicity"的dt元素
            all_dts = soup.find_all('dt')
            for dt in all_dts:
                if dt.get_text(strip=True).lower() == 'genotoxicity':
                    dd = dt.find_next_sibling('dd')
                    if dd:
                        data['Genotoxicity'] = dd.get_text(strip=True)
                        logging.info(f"通过全局搜索找到Genotoxicity标签: {data['Genotoxicity']}")
                        break
                    
        logging.info(f"提取的数据: {data}")
                    
    except Exception as e:
        logging.error(f"解析页面时出错: {e}")
        
    return data

def test_single_url(url):
    """测试单个URL"""
    try:
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'lxml')

        # 调试：查看页面中所有的h3标题
        h3_tags = soup.find_all('h3')
        print(f"页面中的所有h3标题:")
        for h3 in h3_tags:
            print(f"  - {h3.get_text(strip=True)}")

        # 调试：查看所有包含"genotoxicity"的文本
        all_text = soup.get_text().lower()
        if 'genotoxicity' in all_text:
            print("页面包含'genotoxicity'文本")

            # 查找所有dt标签
            all_dts = soup.find_all('dt')
            print(f"页面中的dt标签数量: {len(all_dts)}")
            for dt in all_dts:
                dt_text = dt.get_text(strip=True)
                print(f"  - {dt_text}")
                if 'genotoxicity' in dt_text.lower():
                    dd = dt.find_next_sibling('dd')
                    if dd:
                        print(f"    对应的dd值: {dd.get_text(strip=True)}")

            # 如果没有dt标签，查看页面的整体结构
            if len(all_dts) == 0:
                print("没有找到dt标签，查看页面结构...")
                # 查找包含"genotoxicity"的所有元素
                import re
                genotox_pattern = re.compile(r'genotoxicity', re.IGNORECASE)

                # 查找所有包含genotoxicity的文本节点
                for element in soup.find_all(string=genotox_pattern):
                    parent = element.parent
                    print(f"找到包含genotoxicity的文本: '{element.strip()}'")
                    print(f"  父元素: {parent.name if parent else 'None'}")
                    if parent:
                        print(f"  父元素文本: '{parent.get_text(strip=True)[:100]}...'")

                        # 如果是th标签，查找对应的td值
                        if parent.name == 'th':
                            # 查找同一行的下一个th或td
                            next_cell = parent.find_next_sibling(['th', 'td'])
                            if next_cell:
                                print(f"  第一个对应值: '{next_cell.get_text(strip=True)}'")

                                # 如果第一个值是冒号，继续查找下一个
                                if next_cell.get_text(strip=True) == ':':
                                    next_next_cell = next_cell.find_next_sibling(['th', 'td'])
                                    if next_next_cell:
                                        print(f"  第二个对应值: '{next_next_cell.get_text(strip=True)}'")

                                        # 如果找到的是"Genotoxicity"标签，提取其值
                                        if element.strip().lower() == 'genotoxicity':
                                            genotox_value = next_next_cell.get_text(strip=True)
                                            print(f"  *** 找到Genotoxicity值: {genotox_value} ***")
                                else:
                                    # 如果找到的是"Genotoxicity"标签，提取其值
                                    if element.strip().lower() == 'genotoxicity':
                                        genotox_value = next_cell.get_text(strip=True)
                                        print(f"  *** 找到Genotoxicity值: {genotox_value} ***")
        else:
            print("页面不包含'genotoxicity'文本")

        extracted_data = extract_info_from_page(soup)

        return extracted_data

    except Exception as e:
        logging.error(f"请求失败: {e}")
        return {'Genotoxicity': 'Request Error'}

# 测试一个URL
test_url = "https://www.nite.go.jp/chem/jcheck//direct.action?TYPE=DPAGE2&CAS=1314-98-3&MITI=1-572&ANO=26749"

print("开始测试URL...")
print(f"\n测试URL: {test_url}")
result = test_single_url(test_url)
print(f"最终结果: {result}")
