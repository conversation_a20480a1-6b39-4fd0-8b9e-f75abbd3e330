"""
Main Entry Point for eChemPortal Chemical Data Scraper
Integrates all modules and orchestrates the scraping process
"""
import sys
import time
import signal
from typing import List, Dict, Any
from loguru import logger

import config
from utils import setup_logging, ProgressManager
from level1_scraper import Level1Scraper
from level2_scraper import Level2Scraper
from data_processor import DataProcessor
from error_handler import error_handler, with_error_handling


class ChemicalDataScraper:
    """Main scraper orchestrator"""
    
    def __init__(self):
        self.level1_scraper = None
        self.level2_scraper = Level2Scraper()
        self.data_processor = DataProcessor()
        self.progress_manager = ProgressManager()
        self.should_stop = False
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.should_stop = True
    
    @with_error_handling({'operation': 'main_scraping_process'})
    def run(self):
        """Main scraping process"""
        logger.info("Starting eChemPortal chemical data scraping")
        
        try:
            # Initialize Level 1 scraper
            self.level1_scraper = Level1Scraper()
            
            # Navigate to search page and perform initial search
            if not self._initialize_search():
                logger.error("Failed to initialize search, exiting")
                return False
            
            # Get total pages and resume from last position
            total_pages = self.level1_scraper.get_total_pages()
            start_page = self.progress_manager.get_last_page() + 1
            
            error_handler.progress_monitor.update_progress(
                total_pages=total_pages,
                current_page=start_page - 1
            )
            
            logger.info(f"Starting scraping from page {start_page} of {total_pages}")
            
            # Main scraping loop
            for page_num in range(start_page, total_pages + 1):
                if self.should_stop:
                    logger.info("Stopping scraping due to shutdown signal")
                    break
                
                if not error_handler.should_continue_scraping():
                    logger.error("Stopping scraping due to high error rate")
                    break
                
                success = self._process_page(page_num)
                
                if success:
                    self.progress_manager.update_page(page_num)
                    error_handler.progress_monitor.update_progress(current_page=page_num)
                
                # Save progress periodically
                if page_num % config.SAVE_PROGRESS_EVERY == 0:
                    self.progress_manager.save_progress()
                    error_handler.progress_monitor.print_progress()
                
                # Respectful delay between pages
                if page_num < total_pages:
                    time.sleep(config.DELAY_BETWEEN_PAGES)
            
            # Final save and cleanup
            self._finalize_scraping()
            
            logger.info("Scraping completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Critical error in main scraping process: {e}")
            return False
        
        finally:
            self._cleanup()
    
    def _initialize_search(self) -> bool:
        """Initialize search on eChemPortal"""
        try:
            if not self.level1_scraper.navigate_to_search_page():
                return False
            
            # For now, assume we're starting from a pre-configured search URL
            # In a production version, you might want to perform the actual search
            logger.info("Search initialization completed")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing search: {e}")
            return False
    
    @with_error_handling({'operation': 'process_page'})
    def _process_page(self, page_num: int) -> bool:
        """Process a single page of results"""
        logger.info(f"Processing page {page_num}")
        
        try:
            # Navigate to the page
            if not self.level1_scraper.navigate_to_page(page_num):
                logger.error(f"Failed to navigate to page {page_num}")
                return False
            
            # Extract substances from the page
            substances = self.level1_scraper.extract_substances_from_page()
            
            if not substances:
                logger.warning(f"No substances found on page {page_num}")
                return True
            
            logger.info(f"Found {len(substances)} substances on page {page_num}")
            
            # Process each substance
            page_records = []
            for i, substance in enumerate(substances):
                if self.should_stop:
                    break
                
                records = self._process_substance(substance, page_num, i + 1)
                page_records.extend(records)
                
                error_handler.progress_monitor.update_progress(
                    processed_substances=error_handler.progress_monitor.processed_substances + 1
                )
            
            # Save page data
            if page_records:
                success = self.data_processor.save_data_incremental(page_records)
                if success:
                    error_handler.progress_monitor.update_progress(
                        total_records=error_handler.progress_monitor.total_records + len(page_records)
                    )
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing page {page_num}: {e}")
            return False
    
    @with_error_handling({'operation': 'process_substance'})
    def _process_substance(self, substance: Dict[str, Any], 
                          page_num: int, substance_num: int) -> List[Dict[str, Any]]:
        """Process a single substance"""
        substance_name = substance.get('substance_name', 'Unknown')
        logger.debug(f"Processing substance {substance_num}: {substance_name}")
        
        # Check if already processed
        substance_id = f"{substance.get('cas_number', '')}_{substance_name}"
        if self.progress_manager.is_substance_processed(substance_id):
            logger.debug(f"Substance already processed: {substance_name}")
            return []
        
        try:
            level2_data = []
            
            # If substance has ECHA URL, scrape Level 2 data
            if substance.get('has_acute_toxicity_data') and substance.get('echa_url'):
                echa_url = substance['echa_url']
                logger.debug(f"Scraping ECHA data from: {echa_url}")
                
                level2_data = self.level2_scraper.scrape_echa_page(
                    echa_url, 
                    substance_name, 
                    substance.get('cas_number', '')
                )
                
                if level2_data:
                    error_handler.progress_monitor.update_progress(
                        successful_extractions=error_handler.progress_monitor.successful_extractions + 1
                    )
                else:
                    error_handler.progress_monitor.update_progress(
                        failed_extractions=error_handler.progress_monitor.failed_extractions + 1
                    )
                
                # Respectful delay between ECHA requests
                time.sleep(config.DELAY_BETWEEN_ECHA_REQUESTS)
            
            # Process and integrate data
            processed_records = self.data_processor.process_substance_data(substance, level2_data)
            
            # Mark as processed
            self.progress_manager.add_processed_substance(substance_id)
            
            return processed_records
            
        except Exception as e:
            logger.error(f"Error processing substance {substance_name}: {e}")
            error_handler.progress_monitor.update_progress(
                failed_extractions=error_handler.progress_monitor.failed_extractions + 1
            )
            return []
    
    def _finalize_scraping(self):
        """Finalize scraping process"""
        logger.info("Finalizing scraping process...")
        
        # Save final progress
        self.progress_manager.save_progress()
        
        # Print final statistics
        error_handler.progress_monitor.print_progress()
        
        # Save processing statistics
        stats = self.data_processor.get_statistics()
        logger.info(f"Processing statistics: {stats}")
        
        # Save error logs
        self.data_processor.save_error_log()
        error_handler.save_error_log()
    
    def _cleanup(self):
        """Cleanup resources"""
        logger.info("Cleaning up resources...")
        
        if self.level1_scraper:
            self.level1_scraper.close()
        
        error_handler.cleanup()


def main():
    """Main entry point"""
    # Setup logging
    setup_logging()
    
    logger.info("="*60)
    logger.info("eChemPortal Chemical Data Scraper")
    logger.info("="*60)
    
    # Create and run scraper
    scraper = ChemicalDataScraper()
    success = scraper.run()
    
    if success:
        logger.info("Scraping completed successfully!")
        sys.exit(0)
    else:
        logger.error("Scraping failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
