"""
Error Handling and Monitoring Module
Provides robust error handling, retry mechanisms, and progress monitoring
"""
import time
import json
import traceback
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
from loguru import logger
from functools import wraps
from dataclasses import dataclass, asdict
from enum import Enum

import config


class ErrorType(Enum):
    """Types of errors that can occur during scraping"""
    NETWORK_ERROR = "network_error"
    PARSING_ERROR = "parsing_error"
    SELENIUM_ERROR = "selenium_error"
    DATA_VALIDATION_ERROR = "data_validation_error"
    FILE_IO_ERROR = "file_io_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class ErrorRecord:
    """Structure for error records"""
    timestamp: float
    error_type: ErrorType
    error_message: str
    context: Dict[str, Any]
    traceback_info: str
    retry_count: int = 0
    resolved: bool = False


class ProgressMonitor:
    """Monitor and track scraping progress"""
    
    def __init__(self):
        self.start_time = time.time()
        self.current_page = 0
        self.total_pages = 0
        self.processed_substances = 0
        self.successful_extractions = 0
        self.failed_extractions = 0
        self.total_records = 0
        self.last_update = time.time()
        
    def update_progress(self, **kwargs):
        """Update progress metrics"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.last_update = time.time()
    
    def get_progress_stats(self) -> Dict[str, Any]:
        """Get current progress statistics"""
        elapsed_time = time.time() - self.start_time
        
        return {
            'elapsed_time_seconds': elapsed_time,
            'elapsed_time_formatted': self._format_time(elapsed_time),
            'current_page': self.current_page,
            'total_pages': self.total_pages,
            'pages_completed': self.current_page,
            'pages_remaining': max(0, self.total_pages - self.current_page),
            'processed_substances': self.processed_substances,
            'successful_extractions': self.successful_extractions,
            'failed_extractions': self.failed_extractions,
            'success_rate': self._calculate_success_rate(),
            'total_records': self.total_records,
            'estimated_completion_time': self._estimate_completion_time(),
            'last_update': self.last_update
        }
    
    def _format_time(self, seconds: float) -> str:
        """Format time in human-readable format"""
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _calculate_success_rate(self) -> float:
        """Calculate success rate percentage"""
        total_attempts = self.successful_extractions + self.failed_extractions
        if total_attempts == 0:
            return 0.0
        return (self.successful_extractions / total_attempts) * 100
    
    def _estimate_completion_time(self) -> Optional[str]:
        """Estimate completion time based on current progress"""
        if self.current_page == 0 or self.total_pages == 0:
            return None
        
        elapsed_time = time.time() - self.start_time
        pages_per_second = self.current_page / elapsed_time
        
        if pages_per_second > 0:
            remaining_pages = self.total_pages - self.current_page
            estimated_seconds = remaining_pages / pages_per_second
            return self._format_time(estimated_seconds)
        
        return None
    
    def print_progress(self):
        """Print current progress to console"""
        stats = self.get_progress_stats()
        
        print(f"\n{'='*60}")
        print(f"SCRAPING PROGRESS")
        print(f"{'='*60}")
        print(f"Elapsed Time: {stats['elapsed_time_formatted']}")
        print(f"Pages: {stats['current_page']}/{stats['total_pages']} ({stats['pages_remaining']} remaining)")
        print(f"Substances Processed: {stats['processed_substances']}")
        print(f"Successful Extractions: {stats['successful_extractions']}")
        print(f"Failed Extractions: {stats['failed_extractions']}")
        print(f"Success Rate: {stats['success_rate']:.1f}%")
        print(f"Total Records: {stats['total_records']}")
        
        if stats['estimated_completion_time']:
            print(f"Estimated Completion: {stats['estimated_completion_time']}")
        
        print(f"{'='*60}\n")


class ErrorHandler:
    """Handle errors with retry logic and logging"""
    
    def __init__(self):
        self.error_records: List[ErrorRecord] = []
        self.error_counts = {error_type: 0 for error_type in ErrorType}
        self.progress_monitor = ProgressMonitor()
        
    def retry_with_backoff(self, max_retries: int = None, 
                          backoff_factor: float = 2.0,
                          exceptions: tuple = (Exception,)):
        """Decorator for retry logic with exponential backoff"""
        max_retries = max_retries or config.MAX_RETRIES
        
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e
                        
                        if attempt < max_retries:
                            delay = config.RETRY_DELAY * (backoff_factor ** attempt)
                            logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}")
                            logger.info(f"Retrying in {delay:.1f} seconds...")
                            time.sleep(delay)
                        else:
                            logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
                
                # If all retries failed, log the error and re-raise
                self.log_error(
                    error_type=self._classify_error(last_exception),
                    error_message=str(last_exception),
                    context={'function': func.__name__, 'args': str(args)[:200]},
                    exception=last_exception
                )
                raise last_exception
            
            return wrapper
        return decorator
    
    def safe_execute(self, func: Callable, *args, 
                    error_context: Dict[str, Any] = None, **kwargs) -> Optional[Any]:
        """Safely execute a function with error handling"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.log_error(
                error_type=self._classify_error(e),
                error_message=str(e),
                context=error_context or {'function': func.__name__},
                exception=e
            )
            return None
    
    def log_error(self, error_type: ErrorType, error_message: str,
                 context: Dict[str, Any] = None, exception: Exception = None):
        """Log an error with full context"""
        error_record = ErrorRecord(
            timestamp=time.time(),
            error_type=error_type,
            error_message=error_message,
            context=context or {},
            traceback_info=traceback.format_exc() if exception else ""
        )
        
        self.error_records.append(error_record)
        self.error_counts[error_type] += 1
        
        # Log to file
        logger.error(f"[{error_type.value}] {error_message}")
        if context:
            logger.error(f"Context: {context}")
        
        # Log critical errors immediately
        if self._is_critical_error(error_type):
            self.save_error_log()
    
    def _classify_error(self, exception: Exception) -> ErrorType:
        """Classify exception type"""
        exception_name = type(exception).__name__
        
        if 'network' in exception_name.lower() or 'connection' in exception_name.lower():
            return ErrorType.NETWORK_ERROR
        elif 'selenium' in exception_name.lower() or 'webdriver' in exception_name.lower():
            return ErrorType.SELENIUM_ERROR
        elif 'parse' in exception_name.lower() or 'beautifulsoup' in exception_name.lower():
            return ErrorType.PARSING_ERROR
        elif 'io' in exception_name.lower() or 'file' in exception_name.lower():
            return ErrorType.FILE_IO_ERROR
        elif 'validation' in exception_name.lower():
            return ErrorType.DATA_VALIDATION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def _is_critical_error(self, error_type: ErrorType) -> bool:
        """Check if error type is critical"""
        critical_errors = [ErrorType.FILE_IO_ERROR, ErrorType.SELENIUM_ERROR]
        return error_type in critical_errors
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors"""
        return {
            'total_errors': len(self.error_records),
            'error_counts': {error_type.value: count for error_type, count in self.error_counts.items()},
            'recent_errors': [asdict(error) for error in self.error_records[-10:]],
            'critical_error_count': sum(1 for error in self.error_records 
                                      if self._is_critical_error(error.error_type))
        }
    
    def save_error_log(self):
        """Save error log to file"""
        try:
            error_log_path = config.LOGS_DIR / "error_log.json"
            
            error_data = {
                'timestamp': time.time(),
                'summary': self.get_error_summary(),
                'all_errors': [asdict(error) for error in self.error_records],
                'progress_stats': self.progress_monitor.get_progress_stats()
            }
            
            with open(error_log_path, 'w', encoding='utf-8') as f:
                json.dump(error_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Error log saved to {error_log_path}")
            
        except Exception as e:
            logger.error(f"Failed to save error log: {e}")
    
    def should_continue_scraping(self) -> bool:
        """Determine if scraping should continue based on error rate"""
        total_attempts = (self.progress_monitor.successful_extractions + 
                         self.progress_monitor.failed_extractions)
        
        if total_attempts < 10:  # Not enough data to make decision
            return True
        
        error_rate = self.progress_monitor.failed_extractions / total_attempts
        
        # Stop if error rate is too high
        if error_rate > 0.8:
            logger.error(f"Error rate too high ({error_rate:.1%}), stopping scraping")
            return False
        
        # Stop if too many critical errors
        critical_errors = sum(1 for error in self.error_records 
                            if self._is_critical_error(error.error_type))
        
        if critical_errors > 5:
            logger.error(f"Too many critical errors ({critical_errors}), stopping scraping")
            return False
        
        return True
    
    def cleanup(self):
        """Cleanup and final error reporting"""
        self.save_error_log()
        
        summary = self.get_error_summary()
        logger.info(f"Final error summary: {summary['total_errors']} total errors")
        
        for error_type, count in summary['error_counts'].items():
            if count > 0:
                logger.info(f"  {error_type}: {count}")


# Global error handler instance
error_handler = ErrorHandler()


def with_error_handling(error_context: Dict[str, Any] = None):
    """Decorator to add error handling to functions"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            return error_handler.safe_execute(
                func, *args, 
                error_context=error_context or {'function': func.__name__},
                **kwargs
            )
        return wrapper
    return decorator
