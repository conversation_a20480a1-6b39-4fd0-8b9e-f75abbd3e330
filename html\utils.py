"""
Utility functions for the eChemPortal scraper
"""
import time
import random
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from loguru import logger
import requests
from fake_useragent import UserAgent

import config


class ScrapingUtils:
    """Utility class for common scraping operations"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """Setup requests session with default headers"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        try:
            return self.ua.random
        except:
            return random.choice(config.USER_AGENTS)
    
    def make_request(self, url: str, params: Optional[Dict] = None, 
                    max_retries: int = None) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        max_retries = max_retries or config.MAX_RETRIES
        
        for attempt in range(max_retries):
            try:
                # Rotate user agent
                self.session.headers['User-Agent'] = self.get_random_user_agent()
                
                response = self.session.get(
                    url, 
                    params=params,
                    timeout=config.REQUEST_TIMEOUT
                )
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request attempt {attempt + 1} failed for {url}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(config.RETRY_DELAY * (attempt + 1))
                else:
                    logger.error(f"All {max_retries} attempts failed for {url}")
                    return None
    
    def respectful_delay(self, delay_type: str = "default"):
        """Add respectful delay between requests"""
        delays = {
            "default": config.DELAY_BETWEEN_REQUESTS,
            "page": config.DELAY_BETWEEN_PAGES,
            "echa": config.DELAY_BETWEEN_ECHA_REQUESTS
        }
        
        delay = delays.get(delay_type, config.DELAY_BETWEEN_REQUESTS)
        # Add some randomness to avoid detection
        actual_delay = delay + random.uniform(0, 1)
        time.sleep(actual_delay)


class ProgressManager:
    """Manage scraping progress and resume functionality"""
    
    def __init__(self, progress_file: Path = None):
        self.progress_file = progress_file or config.PROGRESS_FILE
        self.progress_data = self._load_progress()
    
    def _load_progress(self) -> Dict[str, Any]:
        """Load progress from file"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Could not load progress file: {e}")
        
        return {
            "last_page": 0,
            "processed_substances": [],
            "failed_urls": [],
            "total_records": 0,
            "start_time": None,
            "last_update": None
        }
    
    def save_progress(self):
        """Save current progress to file"""
        try:
            self.progress_data["last_update"] = time.time()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save progress: {e}")
    
    def update_page(self, page_num: int):
        """Update last processed page"""
        self.progress_data["last_page"] = page_num
    
    def add_processed_substance(self, substance_id: str):
        """Add processed substance to avoid duplicates"""
        if substance_id not in self.progress_data["processed_substances"]:
            self.progress_data["processed_substances"].append(substance_id)
    
    def add_failed_url(self, url: str, error: str):
        """Record failed URL for later retry"""
        self.progress_data["failed_urls"].append({
            "url": url,
            "error": str(error),
            "timestamp": time.time()
        })
    
    def increment_records(self, count: int = 1):
        """Increment total records count"""
        self.progress_data["total_records"] += count
    
    def is_substance_processed(self, substance_id: str) -> bool:
        """Check if substance was already processed"""
        return substance_id in self.progress_data["processed_substances"]
    
    def get_last_page(self) -> int:
        """Get last processed page number"""
        return self.progress_data.get("last_page", 0)


def clean_text(text: str) -> str:
    """Clean and normalize text data"""
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    cleaned = " ".join(text.strip().split())
    
    # Remove common unwanted characters
    cleaned = cleaned.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    
    return cleaned


def extract_cas_number(text: str) -> Optional[str]:
    """Extract CAS number from text using pattern matching"""
    import re
    
    # CAS number pattern: XXX-XX-X or XXXX-XX-X or XXXXX-XX-X
    cas_pattern = r'\b\d{2,7}-\d{2}-\d\b'
    match = re.search(cas_pattern, text)
    
    return match.group(0) if match else None


def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add file handler
    logger.add(
        config.ERROR_LOG_FILE,
        format=config.LOG_FORMAT,
        level=config.LOG_LEVEL,
        rotation="10 MB",
        retention="30 days"
    )
    
    # Add console handler
    logger.add(
        lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}",
        level=config.LOG_LEVEL
    )
