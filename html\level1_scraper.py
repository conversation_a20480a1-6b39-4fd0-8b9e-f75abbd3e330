"""
Level 1 Scraper: eChemPortal property search results
Extracts chemical substance information and ECHA links
"""
import time
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
from loguru import logger
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

import config
from utils import ScrapingUtils, clean_text, extract_cas_number


class Level1Scraper:
    """Scraper for eChemPortal property search results"""
    
    def __init__(self):
        self.utils = ScrapingUtils()
        self.driver = None
        self._setup_selenium()
    
    def _setup_selenium(self):
        """Setup Selenium WebDriver"""
        chrome_options = Options()
        if config.HEADLESS_MODE:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument(f"--user-agent={self.utils.get_random_user_agent()}")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(config.SELENIUM_PAGE_LOAD_TIMEOUT)
            logger.info("Selenium WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Selenium WebDriver: {e}")
            raise
    
    def navigate_to_search_page(self) -> bool:
        """Navigate to eChemPortal property search page"""
        try:
            logger.info("Navigating to eChemPortal property search page")
            self.driver.get(config.ECHEMPORTAL_BASE_URL)
            
            # Wait for page to load
            WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info("Successfully loaded eChemPortal search page")
            return True
            
        except TimeoutException:
            logger.error("Timeout waiting for eChemPortal page to load")
            return False
        except Exception as e:
            logger.error(f"Error navigating to search page: {e}")
            return False
    
    def perform_search(self) -> bool:
        """Perform search for acute oral toxicity data"""
        try:
            logger.info("Performing search for acute oral toxicity data")
            
            # Look for property search form
            # This might need adjustment based on actual page structure
            property_input = WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                EC.presence_of_element_located((By.NAME, "property_name"))
            )
            
            property_input.clear()
            property_input.send_keys(config.TARGET_PROPERTY)
            
            # Submit search
            search_button = self.driver.find_element(By.XPATH, "//input[@type='submit' or @type='button']")
            search_button.click()
            
            # Wait for results to load
            WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                EC.presence_of_element_located((By.CLASS_NAME, "search-results"))
            )
            
            logger.info("Search completed successfully")
            return True
            
        except (TimeoutException, NoSuchElementException) as e:
            logger.error(f"Error performing search: {e}")
            return False
    
    def get_total_pages(self) -> int:
        """Get total number of result pages"""
        try:
            # Look for pagination info
            pagination_elements = self.driver.find_elements(By.CLASS_NAME, "pagination")
            if not pagination_elements:
                logger.warning("No pagination found, assuming single page")
                return 1
            
            # Extract page numbers from pagination
            page_links = pagination_elements[0].find_elements(By.TAG_NAME, "a")
            page_numbers = []
            
            for link in page_links:
                try:
                    page_num = int(link.text.strip())
                    page_numbers.append(page_num)
                except ValueError:
                    continue
            
            total_pages = max(page_numbers) if page_numbers else 1
            logger.info(f"Found {total_pages} total pages")
            return total_pages
            
        except Exception as e:
            logger.error(f"Error getting total pages: {e}")
            return 1
    
    def navigate_to_page(self, page_num: int) -> bool:
        """Navigate to specific results page"""
        try:
            logger.info(f"Navigating to page {page_num}")
            
            # Method 1: Try URL parameter approach
            current_url = self.driver.current_url
            if "pageNumber=" in current_url:
                # Replace existing page number
                import re
                new_url = re.sub(r'pageNumber=\d+', f'pageNumber={page_num-1}', current_url)
            else:
                # Add page number parameter
                separator = "&" if "?" in current_url else "?"
                new_url = f"{current_url}{separator}pageNumber={page_num-1}"
            
            self.driver.get(new_url)
            
            # Wait for results to load
            WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                EC.presence_of_element_located((By.CLASS_NAME, "search-results"))
            )
            
            logger.info(f"Successfully navigated to page {page_num}")
            return True
            
        except Exception as e:
            logger.error(f"Error navigating to page {page_num}: {e}")
            return False
    
    def extract_substances_from_page(self) -> List[Dict[str, str]]:
        """Extract substance information from current page"""
        substances = []
        
        try:
            # Get page source and parse with BeautifulSoup
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'lxml')
            
            # Find substance entries
            # This selector might need adjustment based on actual HTML structure
            substance_entries = soup.find_all('div', class_=['result-item', 'substance-entry', 'search-result'])
            
            if not substance_entries:
                # Try alternative selectors
                substance_entries = soup.find_all('tr', class_='result-row')
                if not substance_entries:
                    substance_entries = soup.find_all('div', class_='substance')
            
            logger.info(f"Found {len(substance_entries)} substance entries on page")
            
            for entry in substance_entries:
                substance_data = self._extract_substance_data(entry)
                if substance_data:
                    substances.append(substance_data)
            
        except Exception as e:
            logger.error(f"Error extracting substances from page: {e}")
        
        return substances
    
    def _extract_substance_data(self, entry) -> Optional[Dict[str, str]]:
        """Extract data from a single substance entry"""
        try:
            substance_data = {}
            
            # Extract substance name (IUPAC name)
            name_element = entry.find(['h2', 'h3', 'strong'], class_=['substance-name', 'iupac-name'])
            if not name_element:
                name_element = entry.find(['h2', 'h3', 'strong'])
            
            if name_element:
                substance_data['substance_name'] = clean_text(name_element.get_text())
            else:
                logger.warning("Could not find substance name in entry")
                return None
            
            # Extract CAS number
            cas_text = entry.get_text()
            cas_number = extract_cas_number(cas_text)
            substance_data['cas_number'] = cas_number or 'N/A'
            
            # Find acute toxicity oral link
            toxicity_link = self._find_toxicity_link(entry)
            if toxicity_link:
                substance_data['echa_url'] = toxicity_link
                substance_data['has_acute_toxicity_data'] = True
            else:
                substance_data['has_acute_toxicity_data'] = False
                logger.debug(f"No acute toxicity link found for {substance_data['substance_name']}")
            
            # Extract source information
            source_element = entry.find(text=lambda text: text and config.TARGET_SOURCE in text)
            substance_data['source'] = config.TARGET_SOURCE if source_element else 'Unknown'
            
            return substance_data
            
        except Exception as e:
            logger.error(f"Error extracting substance data: {e}")
            return None
    
    def _find_toxicity_link(self, entry) -> Optional[str]:
        """Find the acute toxicity oral link in substance entry"""
        try:
            # Look for links containing target property text
            links = entry.find_all('a', href=True)
            
            for link in links:
                link_text = clean_text(link.get_text())
                if config.TARGET_PROPERTY.lower() in link_text.lower():
                    # Verify it's an ECHA link
                    href = link['href']
                    if 'echa.europa.eu' in href or href.startswith('/'):
                        # Convert relative URLs to absolute
                        if href.startswith('/'):
                            href = urljoin(config.ECHA_BASE_URL, href)
                        return href
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding toxicity link: {e}")
            return None
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed successfully")
            except Exception as e:
                logger.error(f"Error closing WebDriver: {e}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
